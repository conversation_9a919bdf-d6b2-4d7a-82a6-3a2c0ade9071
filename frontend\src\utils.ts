import { message, Modal } from 'antd';
import DOMPurify from 'dompurify'; //可以使用第三方库，例如 DOMPurify 防止恶意代码的注入
import { querySensitiveWord, querySensitiveWordPost } from './api/help';
import { entityType } from './types/entityTypes';
let asyncLoadedScripts: any = {};
let asyncLoadedScriptsCallbackQueue: any = {};

export const copyObject: <T>(obj: T) => T = oldObj => {
  if (oldObj instanceof Object) {
    return JSON.parse(JSON.stringify(oldObj));
  } else {
    return oldObj;
  }
};

export const getType = (sType: string): entityType => {
  switch (sType) {
    case 'biz_sobey_video':
      return 'video';
    case 'biz_sobey_audio':
      return 'audio';
    case 'biz_sobey_picture':
      return 'picture';
    case 'biz_sobey_document':
      return 'document';
    case 'folder':
      return 'folder';
    case 'biz_sobey_course':
      return 'course';
    case 'biz_sobey_other':
      return 'other';
    default:
      return null;
  }
};

// 防抖
export const debounce = (fn: () => any, delay: number): (() => void) => {
  let timer: NodeJS.Timeout | null = null;
  return () => {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(fn, delay);
  };
};

export const getScriptDomFromUrl = (url: string, rel?: string) => {
  let dom;
  if (/.+\.js$/.test(url)) {
    dom = document.createElement('SCRIPT');
    dom.setAttribute('type', 'text/javascript');
    dom.setAttribute('src', url);
  } //css
  else {
    dom = document.createElement('link');
    dom.href = url;
    dom.type = 'text/css';
    dom.rel = rel || 'stylesheet';
  }
  return dom;
};

/**
 * 异步加载script
 * @param url
 * @param callback
 */
export const asyncLoadScript = (
  url: string,
  callback?: (() => void) | undefined,
): Promise<any> => {
  return new Promise(resolve => {
    if (asyncLoadedScripts[url] !== undefined) {
      // 已加载script标签
      if (callback && typeof callback === 'function') {
        if (asyncLoadedScripts[url] === 0) {
          // 未执行首个script标签的回调
          if (!asyncLoadedScriptsCallbackQueue[url]) {
            asyncLoadedScriptsCallbackQueue[url] = [];
          }
          asyncLoadedScriptsCallbackQueue[url].push(callback);
        } else {
          callback.apply(window, []);
        }
      }
      resolve();
      return;
    }
    asyncLoadedScripts[url] = 0;
    const scriptDom: any = getScriptDomFromUrl(url);
    if (scriptDom.readyState) {
      scriptDom.onreadystatechange = () => {
        if (
          scriptDom.readyState === 'loaded' ||
          scriptDom.readyState === 'complete'
        ) {
          scriptDom.onreadystatechange = null;
          asyncLoadedScripts[url] = 1;
          resolve();
          if (callback && typeof callback === 'function') {
            callback.apply(window, []);
          }
          if (asyncLoadedScriptsCallbackQueue[url]) {
            for (
              let i = 0, j = asyncLoadedScriptsCallbackQueue[url].length;
              i < j;
              i++
            ) {
              asyncLoadedScriptsCallbackQueue[url][i].apply(window, []);
            }
            asyncLoadedScriptsCallbackQueue[url] = undefined;
          }
        }
      };
    } else {
      scriptDom.onload = () => {
        asyncLoadedScripts[url] = 1;
        resolve();
        if (callback && typeof callback === 'function') {
          callback.apply(window, []);
        }
        if (asyncLoadedScriptsCallbackQueue[url]) {
          for (
            let i = 0, j = asyncLoadedScriptsCallbackQueue[url].length;
            i < j;
            i++
          ) {
            asyncLoadedScriptsCallbackQueue[url][i].apply(window, []);
          }
          asyncLoadedScriptsCallbackQueue[url] = undefined;
        }
      };
    }
    document.getElementsByTagName('head')[0].appendChild(scriptDom);
  });
};

export const asyncLoadScriptArr = (scriptArr: string[]) => {
  let promises: any[] = [];
  const loop = (i: number) => {
    promises.push(
      new Promise((resolve, reject) => {
        asyncLoadScript(scriptArr[i], () => {
          resolve();
        });
      }),
    );
  };
  for (let i = 0; i < scriptArr.length; i++) {
    loop(i);
  }
  return Promise.all(promises);
};

export const changesize = (limit: any) => {
  let item = Number(limit);
  let size;
  if (item < 0.1 * 1024) {
    //小于0.1KB，则转化成B
    size = item.toFixed(2) + 'B';
  } else if (item < 0.1 * 1024 * 1024) {
    //小于0.1MB，则转化成KB
    size = (item / 1024).toFixed(2) + 'KB';
  } else if (item < 0.1 * 1024 * 1024 * 1024) {
    //小于0.1GB，则转化成MB
    size = (item / (1024 * 1024)).toFixed(2) + 'MB';
  } else {
    //其他转化成GB
    size = (item / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
  }
  let sizeStr = size + ''; //转成字符串
  let index = sizeStr.indexOf('.'); //获取小数点处的索引
  let dou = sizeStr.substr(index + 1, 2); //获取小数点后两位的值
  if (dou === '00') {
    //判断后两位是否为00，如果是则删除00
    return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2);
  }
  return size;
};
/**
 * 生成唯一id
 * @param length
 */
export const getId = (length: number = 8) =>
  Number(
    Math.random()
      .toString()
      .substr(3, length) + Date.now(),
  ).toString(36);
/**
 * 百纳秒转时间
 * @param ns
 */
export const l100Ns2Tc$1 = (ns: number, framerate?: number): string => {
  return ns ? (window as any).timecodeconvert.l100Ns2Tc$1(ns, framerate) : '';
};

/**
 * 线程睡眠
 * @param ns
 */
export function sleep(ns: number) {
  return new Promise(resolve =>
    setTimeout(() => {
      resolve(null);
    }, ns),
  );
}

/**
 * 加载icon
 * @param url
 */
export const loadFavicon = (url: string) => {
  const icon = document.createElement('link');
  icon.href = url;
  icon.type = 'image/x-icon';
  icon.rel = 'shortcut icon';
  document.getElementsByTagName('head')[0].appendChild(icon);
};

/**************************颜色处理***********************************/
//hex颜色转rgb颜色
export function HexToRgb(str: string) {
  const r = /^#?[0-9A-Fa-f]{6}$/;
  //test方法检查在字符串中是否存在一个模式，如果存在则返回true，否则返回false
  if (!r.test(str)) return [];
  //replace替换查找的到的字符串
  str = str.replace('#', '');
  //match得到查询数组
  let hxs: any = str.match(/../g);
  //alert('bf:'+hxs)
  for (let i = 0; i < 3; i++) hxs[i] = parseInt(hxs[i], 16);
  //alert(parseInt(80, 16))
  //console.log(hxs);
  return hxs;
}

//GRB颜色转Hex颜色
function RgbToHex(a: number, b: number, c: number) {
  const r = /^\d{1,3}$/;
  if (!r.test(a + '') || !r.test(b + '') || !r.test(c + '')) return '';
  const hexs = [a.toString(16), b.toString(16), c.toString(16)];
  for (let i = 0; i < 3; i++) if (hexs[i].length == 1) hexs[i] = '0' + hexs[i];
  return '#' + hexs.join('');
}

//得到hex颜色值为color的加深颜色值，level为加深的程度，限0-1之间
export function getDarkColor(color: string, level: number) {
  const r = /^#?[0-9A-F]{6}$/;
  if (!r.test(color)) return '';
  const rgbc = HexToRgb(color);
  //floor 向下取整
  for (let i = 0; i < 3; i++) rgbc[i] = Math.floor(rgbc[i] * (1 - level));
  return RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
}

//得到hex颜色值为color的减淡颜色值，level为加深的程度，限0-1之间
export function getLightColor(color: string, level: number) {
  const r = /^#?[0-9a-fA-F]{6}$/;
  if (!r.test(color)) return '';
  const rgbc = HexToRgb(color);
  for (let i = 0; i < 3; i++)
    rgbc[i] = Math.floor((255 - rgbc[i]) * level + rgbc[i]);
  return RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
}

/****************************颜色处理结束**************************************/

// 根据文件类型插入不同的标签
const publicPath = '/web';
const ppt = publicPath + '/publisher/ppt.png';
const download = publicPath + '/publisher/download.png';
const herf = publicPath + '/publisher/herf.png';
const word = publicPath + '/publisher/word.png';
const zip = publicPath + '/publisher/zip.png';
const pdf = publicPath + '/publisher/pdf.png';
const excel = publicPath + '/publisher/excel.png';
const preview = publicPath + '/publisher/preview.png';
const rar = publicPath + '/publisher/rar.png';
export const insertdom = (e: any, tiny: string = 'textarea') => {
  let tinymce = (window as any).tinymce.get(`${tiny}`);
  let nowDate = new Date();
  let year = nowDate.getFullYear();
  let month =
    nowDate.getMonth() + 1 < 10
      ? '0' + (nowDate.getMonth() + 1)
      : nowDate.getMonth() + 1;
  let day =
    nowDate.getDate() < 10 ? '0' + nowDate.getDate() : nowDate.getDate();
  let dateStr = year + '/' + month + '/' + day;

  // 图片
  if (
    e.suffix == 'jpg' ||
    e.suffix == 'png' ||
    e.suffix == 'gif' ||
    e.suffix == 'jpeg'
  ) {
    tinymce.insertContent(
      `<img data-resourceid=${e.file.id} style="width:400px" src=${e.uploadurl.data}>`,
    );
  } else if (e.suffix == 'mp3' && e.mp3Icon == 'standardIcon') {
    tinymce.insertContent(
      `<audio data-resourceid=${e.file.id}  controls="controls" controlsList="nodownload" src="${e.uploadurl.data}"></audio><p></p>`,
    );
  } else if (e.suffix == 'mp3' && e.mp3Icon == 'smallIcon') {
    tinymce.insertContent(
      `<span id="sobey_mp3_small" contenteditable="false">
       <audio style="display:none;" controls="controls" controlsList="nodownload" src="${e.uploadurl.data}"></audio>
      </span><span></span>`,
    );
  } else if (e.suffix == 'mp4') {
    tinymce.insertContent(
      `<video data-resourceid=${e.file.id
      } style="object-fit: fill; vertical-align: middle; preload:metadata;" src=${e
        .uploadurl.data +
      '#t=1'} width="640" height="360" controls="controls" controlsList="nodownload" id="js-video"  preload="metadata"  x-webkit-airplay webkit-playsinline loop="loop" controls="controls" x5-video-player-type="h5" x5-video-player-fullscreen="true" x5-video-orientation="portraint" playsinline="true"></video>`,
      // `<video src=${e.uploadurl.data} controls="controls" controlslist='nodownload'>
      // 浏览器不支持播放该视频！
      // </video>`,
    );
  } else if (e.suffix == 'ppt' || e.suffix == 'pptx') {
    tinymce.insertContent(`
      <div data-resourceid=${e.file.id} contenteditable="false" id="enclosure" style="width: 400px;height: 40px;background: #F7F7F7;border-radius: 1px;border: 1px solid #F0F0F0;display: flex;align-items: center;">
          <img src=${ppt} style="width: 22px;height: 22px;margin: 0 0 0 20px;" />
          <span style="width: 200px;font-size: 14px;color: #333333;padding: 0 0 0 10px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">${e.file.name}</span>
          <span style="font-size: 14px;color: #333333;">${dateStr}</span>
          <a href=${e.uploadurl.data} download=${e.file.name}>
            <img src=${download} style="width: 18px;height: 18px;margin: 0 0 0 20px;" />
          </a>
         <a href=${e.uploadurl.data} target="_blank">
            <img src=${preview} style="width: 18px;height: 18px;margin: 0 0 0 8px;" />
          </a>
      </div><p></p>`);
  } else if (e.suffix == 'doc' || e.suffix == 'docx') {
    // <span  contenteditable="false" style="width: 400px;height: 40px;background: #F7F7F7;border-radius: 1px;border: 1px solid #F0F0F0;display: inline-block;">
    //     </span><span>222</span>
    tinymce.insertContent(`
      <div data-resourceid=${e.file.id} contenteditable="false" id="enclosure" style="width: 400px;height: 40px;background: #F7F7F7;border-radius: 1px;border: 1px solid #F0F0F0;display: flex;align-items: center;">
          <img src=${word} style="width: 22px;height: 22px;margin: 0 0 0 20px;" />
          <span style="width: 200px;font-size: 14px;color: #333333;padding: 0 0 0 10px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">${e.file.name}</span>
          <span style="font-size: 14px;color: #333333;">${dateStr}</span>
          <a href=${e.uploadurl.data} download=${e.file.name}>
            <img src=${download} style="width: 18px;height: 18px;margin: 0 0 0 20px;" />
          </a>
         <a href=${e.uploadurl.data} target="_blank">
            <img src=${preview} style="width: 18px;height: 18px;margin: 0 0 0 8px;" />
          </a>
      </div><p></p>`);
  } else if (e.suffix == 'xls' || e.suffix == 'xlsx') {
    tinymce.insertContent(`
      <div data-resourceid=${e.file.id} contenteditable="false" id="enclosure" style="width: 400px;height: 40px;background: #F7F7F7;border-radius: 1px;border: 1px solid #F0F0F0;display: flex;align-items: center;">
          <img src=${excel} style="width: 22px;height: 22px;margin: 0 0 0 20px;" />
          <span style="width: 200px;font-size: 14px;color: #333333;padding: 0 0 0 10px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">${e.file.name}</span>
          <span style="font-size: 14px;color: #333333;">${dateStr}</span>
          <a href=${e.uploadurl.data} download=${e.file.name}>
            <img  src=${download} style="width: 18px;height: 18px;margin: 0 0 0 20px;" />
          </a>
         <a href=${e.uploadurl.data} target="_blank">
            <img src=${preview} style="width: 18px;height: 18px;margin: 0 0 0 8px;" />
          </a>
      </div><p></p>`);
  } else if (e.suffix == 'rar') {
    tinymce.insertContent(`
      <div data-resourceid=${e.file.id} contenteditable="false" id="enclosure" style="width: 400px;height: 40px;background: #F7F7F7;border-radius: 1px;border: 1px solid #F0F0F0;display: flex;align-items: center;">
          <img src=${rar} style="width: 22px;height: 22px;margin: 0 0 0 20px;" />
          <span style="width: 200px;font-size: 14px;color: #333333;padding: 0 0 0 10px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">${e.file.name}</span>
          <span style="font-size: 14px;color: #333333;">${dateStr}</span>
          <a href=${e.uploadurl.data} download=${e.file.name}>
            <img  src=${download} style="width: 18px;height: 18px;margin: 0 0 0 20px;" />
          </a>
      </div><p></p>`);
  } else if (e.suffix == 'pdf') {
    // tinymce.insertContent(`<iframe src="${e.uploadurl.data}" frameborder="0" style="width:100%;height:1000px;margin:0,padding:0;border:0" width="100%" height="1000px"></iframe>`)
    tinymce.insertContent(`
   <div data-resourceid=${e.file.id} contenteditable="false" id="enclosure" style="width: 400px;height: 40px;background: #F7F7F7;border-radius: 1px;border: 1px solid #F0F0F0;display: flex;align-items: center;">
          <img src=${pdf} style="width: 22px;height: 22px;margin: 0 0 0 20px;" />
          <span style="width: 200px;font-size: 14px;color: #333333;padding: 0 0 0 10px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">${e.file.name}</span>
          <span style="font-size: 14px;color: #333333;">${dateStr}</span>
          <a href=${e.uploadurl.data} download=${e.file.name}>
            <img  src=${download} style="width: 18px;height: 18px;margin: 0 0 0 20px;" />
          </a>
          <a href=${e.uploadurl.data} target="_blank">
            <img src=${preview} style="width: 18px;height: 18px;margin: 0 0 0 8px;" />
          </a>
      </div><p></p>`);
  } else if (e.suffix == 'zip') {
    tinymce.insertContent(`
      <div data-resourceid=${e.file.id} contenteditable="false" id="enclosure" style="width: 400px;height: 40px;background: #F7F7F7;border-radius: 1px;border: 1px solid #F0F0F0;display: flex;align-items: center;">
          <img src=${zip} style="width: 22px;height: 22px;margin: 0 0 0 20px;" />
          <span style="width: 200px;font-size: 14px;color: #333333;padding: 0 0 0 10px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">${e.file.name}</span>
          <span style="font-size: 14px;color: #333333;">${dateStr}</span>
          <a href=${e.uploadurl.data} download=${e.file.name}>
            <img src=${download} style="width: 18px;height: 18px;margin: 0 0 0 20px;" />
          </a>
      </div><p></p>`);
  } else {
    tinymce.insertContent(`
      <div data-resourceid=${e.file.id} contenteditable="false" id="enclosure" style="width: 400px;height: 40px;background: #F7F7F7;border-radius: 1px;border: 1px solid #F0F0F0;display: flex;align-items: center;">
          <img  src=${herf} style="width: 22px;height: 22px;margin: 0 0 0 20px;" />
          <span style="width: 200px;font-size: 14px;color: #333333;padding: 0 0 0 10px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;">${e.file.name}</span>
      </div><p></p>`);
  }
};

//转换
export const codeToName = (codes: any) => {
  let temp: any = [];
  if (!codes) return;
  for (let j = 0; j < codes.length; j++) {
    const result = codes[j].split(',')[1]
    if (result) temp.push(result);
  }
  return temp.join(',');
}
//html防注入转换
//这么做的意义在于，当你不是有意地使用 <div dangerouslySetInnerHTML={createMarkup()} /> 时候，
// 它并不会被渲染，因为 createMarkup() 返回的格式是 字符串 而不是一个 {__html: ''} 对象。{__html:...}
// 背后的目的是表明它会被当成 "type/taint" 类型处理。 这种包裹对象，可以通过方法调用返回净化后的数据，随后这种标记过的数据可以被传递给dangerouslySetInnerHTML
export const createMarkup = (detail: any) => {
  return { __html: DOMPurify.sanitize(detail) };
};
// 处理文本字数限制（用于input和paste事件）
export const textInit = (e: any, editor: any, textSetting?: any) => {
  // 处理input和paste事件的字数限制检查
  if ((e.type === 'input' || e.type === 'paste') && textSetting) {
    // 对于paste事件，需要延迟执行以确保内容已经被插入
    const checkAndTruncate = () => {
      const count = textSetting.spaces ? editor.plugins.wordcount.body.getCharacterCount()
        : editor.plugins.wordcount.body.getCharacterCountWithoutSpaces();
      if (count > textSetting.max) {
        textSetting.toast();
        // 获取当前内容并截取到最大长度
        const currentContent = editor.getContent({ format: 'text' });
        const truncatedContent = currentContent.slice(0, textSetting.max);
        editor.setContent(truncatedContent);
        editor.getBody().blur();
      }
    };

    if (e.type === 'paste') {
      // 粘贴事件需要延迟执行，等待内容插入完成
      setTimeout(checkAndTruncate, 0);
    } else {
      // input事件直接执行
      checkAndTruncate();
    }
  }
};
export const noSupport = () => {
  message.info('暂不支持手机端，请前往电脑端操作');
};


// 节流
export const throttle = (fn: any, delay: any) => {
  let timer: any = null;
  return function () {
    if (!timer) {
      timer = setTimeout(() => {
        fn.apply(this, arguments);
        timer = null;
      }, delay);
    }
  };
}

/**
 * 敏感词检测
 * @param value 要校验的值
 * @param text 检验的中文
 * @param successCallback 校验成功的回调 
 * @param failedCallback  失败回调
 */

export const getSensitiveWord = (value: string, text: string, successCallback: () => void, failedCallback?: () => void) => {
  if (value !== "") {
    return querySensitiveWord(value.replace(RegExp("<.+?>", "g"), "")).then((res: any) => {
      if (res.statusCode === 200 && res.data?.length === 0) {
        return successCallback();
      } else if (res.data?.length > 0) {
        return new Promise((resolve) => {
          Modal.confirm({
            content: `${text}包含敏感词：${Array.from(new Set(res.data.map((item: any) => item.matchKeyword))).join("、")}，确认继续执行吗？`,
            onOk() {
              resolve(true);
            },
            onCancel() {
              resolve(false);
            },
            zIndex: 2000,
          })
        }).then((confirm?: any) => {
          if (confirm) {
            return successCallback();
          } else {
            return failedCallback?.();
          }
        })

      }
    })
  } else {
    return Promise.resolve(successCallback())
  }
}

/**
 * 敏感词检测 检测大数据的情况
 * @param value 要校验的值
 * @param text 检验的中文
 * @param successCallback 校验成功的回调 
 * @param failedCallback  失败回调
 */

export const getSensitiveWordPost = (value: string, text: string, successCallback: () => void, failedCallback?: () => void) => {
  if (value !== "") {
    return querySensitiveWordPost(value.replace(RegExp("<.+?>", "g"), "")).then((res: any) => {
      if (res.statusCode === 200 && res.data?.length === 0) {
        return successCallback();
      } else if (res.data?.length > 0) {
        return new Promise((resolve) => {
          Modal.confirm({
            content: `${text}包含敏感词：${Array.from(new Set(res.data.map((item: any) => item.matchKeyword))).join("、")}，确认继续执行吗？`,
            onOk() {
              resolve(true);
            },
            onCancel() {
              resolve(false);
            },
            zIndex: 2000,
          })
        }).then((confirm?: any) => {
          if (confirm) {
            return successCallback();
          } else {
            return failedCallback?.();
          }
        })

      }
    })
  } else {
    return Promise.resolve(successCallback())
  }
}

/**
 * 将数字转换为中文
 */
export const convertToChinaNum = (num: number) => {
  var arr1 = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  var arr2 = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿'];//可继续追加更高位转换值
  if (!num || isNaN(num)) {
    return "零";
  }
  var english = num.toString().split("")
  var result = "";
  for (var i = 0; i < english.length; i++) {
    var des_i = english.length - 1 - i;//倒序排列设值
    result = arr2[i] + result;
    var arr1_index = english[des_i];
    result = arr1[Number(arr1_index)] + result;
  }
  //将【零千、零百】换成【零】 【十零】换成【十】
  result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十');
  //合并中间多个零为一个零
  result = result.replace(/零+/g, '零');
  //将【零亿】换成【亿】【零万】换成【万】
  result = result.replace(/零亿/g, '亿').replace(/零万/g, '万');
  //将【亿万】换成【亿】
  result = result.replace(/亿万/g, '亿');
  //移除末尾的零
  result = result.replace(/零+$/, '')
  //将【零一十】换成【零十】
  //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
  //将【一十】换成【十】
  result = result.replace(/^一十/g, '十');
  return result;
}


/**
 * 将时间戳
 */

export const formatDate = (timestamp: string | number | Date) => {
  const date = new Date(timestamp);  // 将毫秒时间戳转换为 Date 对象
  return date.toLocaleString('zh-CN', {  // 格式化为中文时间
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false  // 使用 24 小时制
  });
}

// webpack相对路径转绝对路径,使用webpack nodejs处理,如果是node_modules中则使用编译后路径
export function isNil(val: any) {
  return val === undefined || val === null;
}

export const getUuid = () => {
  if (typeof crypto === 'object') {
    if (typeof crypto.randomUUID === 'function') {
      return crypto.randomUUID().replaceAll("-", "");
    }
    if (
      typeof crypto.getRandomValues === 'function' &&
      typeof Uint8Array === 'function'
    ) {
      const callback = (c: any) => {
        const num = Number(c);
        return (
          num ^
          (crypto.getRandomValues(new Uint8Array(1))[0] &
            (15 >> (num / 4)))
        ).toString(16);
      };
      return (
        '' +
        [1e7] +
        -1e3 +
        -4e3 +
        -8e3 +
        -1e11
      ).replace(/[018]/g, callback).replaceAll("-", "");
    }
  }
  let timestamp = new Date().getTime();
  let perForNow =
    (typeof performance !== 'undefined' &&
      performance.now &&
      performance.now() * 1000) ||
    0;
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    (c) => {
      let random = Math.random() * 16;
      if (timestamp > 0) {
        random = (timestamp + random) % 16 | 0;
        timestamp = Math.floor(timestamp / 16);
      } else {
        random = (perForNow + random) % 16 | 0;
        perForNow = Math.floor(perForNow / 16);
      }
      return (c === 'x'
        ? random
        : (random & 0x3) | 0x8
      ).toString(16);
    },
  ).replaceAll("-", "");
};

export const options = [
  { id: 'first', questions_type: '单选', label: '单选', num: 1, value: 0 },
  { id: 'second', questions_type: '多选', label: '多选', num: 1, value: 1 },
  { id: 'third', questions_type: '填空', label: '填空', num: 1, value: 2 },
  { id: 'fourth', questions_type: '主观', label: '主观', num: 1, value: 3 },
  { id: 'fifth', questions_type: '判断', label: '判断', num: 1, value: 4 },
]

export const languageOptions = [
  { label: '中文', value: 1 },
  { label: '英文', value: 2 }
]
export const cognitive_levelOptions = [
  { value: '记忆', label: '记忆' },
  { value: '理解', label: '理解' },
  { value: '应用', label: '应用' },
  { value: '分析', label: '分析' },
  { value: '评价', label: '评价' },
  { value: '创新', label: '创新' },
]
export const questions_difficultyOptions = [
  { value: 1, label: '1' },
  { value: 2, label: '2' },
  { value: 3, label: '3' },
  { value: 4, label: '4' },
  { value: 5, label: '5' }
];
export const questions_levelOptions = [
  { value: '本科生', label: '本科生' },
  { value: '研究生', label: '研究生' },
]

export const radioOptions = [
  {
    label: '正确',
    value: true
  },
  {
    label: '错误',
    value: false
  }
]

